// Interface for uploaded video data
export interface UploadedVideo {
  id: string;
  file: File;
  name: string;
  src: string; // Object URL for preview
  duration: number; // Duration in seconds
  durationInFrames: number; // Duration in frames (30fps)
  // Trimming properties (optional)
  trimStart?: number; // Start frame for trimming (default: 0)
  trimEnd?: number; // End frame for trimming (default: durationInFrames)
}
