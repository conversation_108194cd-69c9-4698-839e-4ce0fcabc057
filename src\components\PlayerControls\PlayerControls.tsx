import React, { useCallback, useEffect, useState, useRef } from 'react';
import type { PlayerRef } from '@remotion/player';

interface PlayerControlsProps {
  playerRef: React.RefObject<PlayerRef | null>;
  durationInFrames: number;
  fps: number;
}

export const PlayerControls: React.FC<PlayerControlsProps> = ({
  playerRef,
  durationInFrames,
  fps
}) => {
  const [playing, setPlaying] = useState(false);
  const [currentFrame, setCurrentFrame] = useState(0);
  const [volume, setVolume] = useState(1);
  const [muted, setMuted] = useState(false);
  const [loop, setLoop] = useState(false);

  // Format time helper function
  const formatTime = useCallback((frame: number, fps: number): string => {
    const totalSeconds = frame / fps;
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = Math.floor(totalSeconds % 60);
    const remainingFrames = Math.round(frame % fps);
    
    const minutesStr = String(minutes).padStart(2, '0');
    const secondsStr = String(seconds).padStart(2, '0');
    const framesStr = String(remainingFrames).padStart(2, '0');
    
    return `${minutesStr}:${secondsStr}.${framesStr}`;
  }, []);

  // Set up event listeners for player state changes
  useEffect(() => {
    const { current } = playerRef;
    if (!current) return;

    // Initialize state from player
    setPlaying(current.isPlaying());
    setCurrentFrame(current.getCurrentFrame());
    setVolume(current.getVolume());
    setMuted(current.isMuted());

    // Event handlers
    const onPlay = () => setPlaying(true);
    const onPause = () => setPlaying(false);
    const onFrameUpdate = () => setCurrentFrame(current.getCurrentFrame());
    const onVolumeChange = () => setVolume(current.getVolume());
    const onMuteChange = () => setMuted(current.isMuted());

    // Add event listeners
    current.addEventListener('play', onPlay);
    current.addEventListener('pause', onPause);
    current.addEventListener('frameupdate', onFrameUpdate);
    current.addEventListener('volumechange', onVolumeChange);
    current.addEventListener('mutechange', onMuteChange);

    return () => {
      current.removeEventListener('play', onPlay);
      current.removeEventListener('pause', onPause);
      current.removeEventListener('frameupdate', onFrameUpdate);
      current.removeEventListener('volumechange', onVolumeChange);
      current.removeEventListener('mutechange', onMuteChange);
    };
  }, [playerRef]);

  // Control handlers
  const handlePlayPause = useCallback(() => {
    playerRef.current?.toggle();
  }, [playerRef]);

  const handleSeek = useCallback((frame: number) => {
    const clampedFrame = Math.max(0, Math.min(frame, durationInFrames - 1));
    playerRef.current?.seekTo(clampedFrame);
  }, [playerRef, durationInFrames]);

  const handleVolumeChange = useCallback((newVolume: number) => {
    if (!playerRef.current) return;
    
    if (newVolume > 0 && playerRef.current.isMuted()) {
      playerRef.current.unmute();
    }
    playerRef.current.setVolume(newVolume);
  }, [playerRef]);

  const handleMuteToggle = useCallback(() => {
    if (!playerRef.current) return;
    
    if (playerRef.current.isMuted()) {
      playerRef.current.unmute();
    } else {
      playerRef.current.mute();
    }
  }, [playerRef]);

  const handleLoopToggle = useCallback(() => {
    setLoop(prev => !prev);
    // Note: Loop functionality would need to be implemented in the parent component
    // as it requires updating the Player component's loop prop
  }, []);

  // Seek bar click handler
  const handleSeekBarClick = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = clickX / rect.width;
    const targetFrame = Math.round(percentage * (durationInFrames - 1));
    handleSeek(targetFrame);
  }, [durationInFrames, handleSeek]);

  // Calculate progress percentage
  const progressPercentage = durationInFrames > 0 ? (currentFrame / (durationInFrames - 1)) * 100 : 0;

  return (
    <div style={{
      backgroundColor: '#2c3e50',
      color: 'white',
      padding: '12px 16px',
      borderRadius: '8px',
      marginTop: '12px',
      display: 'flex',
      alignItems: 'center',
      gap: '16px',
      fontSize: '14px'
    }}>
      {/* Play/Pause Button */}
      <button
        onClick={handlePlayPause}
        style={{
          backgroundColor: '#3498db',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          padding: '8px 12px',
          cursor: 'pointer',
          fontSize: '14px',
          fontWeight: 'bold',
          minWidth: '60px'
        }}
        title={playing ? 'Pause' : 'Play'}
      >
        {playing ? '⏸️' : '▶️'}
      </button>

      {/* Time Display */}
      <div style={{
        fontFamily: 'monospace',
        fontSize: '12px',
        minWidth: '120px',
        color: '#ecf0f1'
      }}>
        {formatTime(currentFrame, fps)} / {formatTime(durationInFrames, fps)}
      </div>

      {/* Seek Bar */}
      <div style={{
        flex: 1,
        height: '20px',
        display: 'flex',
        alignItems: 'center'
      }}>
        <div
          onClick={handleSeekBarClick}
          style={{
            width: '100%',
            height: '6px',
            backgroundColor: '#34495e',
            borderRadius: '3px',
            cursor: 'pointer',
            position: 'relative'
          }}
        >
          {/* Progress fill */}
          <div
            style={{
              width: `${progressPercentage}%`,
              height: '100%',
              backgroundColor: '#3498db',
              borderRadius: '3px',
              transition: 'width 0.1s ease'
            }}
          />
          
          {/* Playhead knob */}
          <div
            style={{
              position: 'absolute',
              left: `${progressPercentage}%`,
              top: '50%',
              transform: 'translate(-50%, -50%)',
              width: '14px',
              height: '14px',
              backgroundColor: '#3498db',
              borderRadius: '50%',
              border: '2px solid white',
              cursor: 'pointer'
            }}
          />
        </div>
      </div>

      {/* Volume Controls */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }}>
        <button
          onClick={handleMuteToggle}
          style={{
            backgroundColor: 'transparent',
            color: 'white',
            border: '1px solid #7f8c8d',
            borderRadius: '4px',
            padding: '4px 8px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
          title={muted ? 'Unmute' : 'Mute'}
        >
          {muted ? '🔇' : '🔊'}
        </button>
        
        <input
          type="range"
          min={0}
          max={1}
          step={0.01}
          value={muted ? 0 : volume}
          onChange={(e) => handleVolumeChange(Number(e.target.value))}
          style={{
            width: '60px',
            cursor: 'pointer'
          }}
          title="Volume"
        />
      </div>

      {/* Loop Toggle */}
      <button
        onClick={handleLoopToggle}
        style={{
          backgroundColor: loop ? '#27ae60' : 'transparent',
          color: 'white',
          border: '1px solid #7f8c8d',
          borderRadius: '4px',
          padding: '4px 8px',
          cursor: 'pointer',
          fontSize: '12px'
        }}
        title={loop ? 'Loop enabled' : 'Loop disabled'}
      >
        🔁
      </button>
    </div>
  );
};
