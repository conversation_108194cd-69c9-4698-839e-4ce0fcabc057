import React, { useState } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  horizontalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import type { UploadedVideo } from '../../types/video';

// Sortable Video Clip Component
interface SortableVideoClipProps {
  video: UploadedVideo;
  index: number;
  left: number;
  width: number;
  timelineHeight: number;
  onVideoDelete?: (videoId: string) => void;
  onVideoTrim?: (videoId: string) => void;
}

const SortableVideoClip: React.FC<SortableVideoClipProps> = ({
  video,
  index,
  left,
  width,
  timelineHeight,
  onVideoDelete,
  onVideoTrim
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: video.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={{
        position: 'absolute',
        left: `${left}px`,
        top: '4px',
        width: `${width}px`,
        height: `${timelineHeight - 8}px`,
        backgroundColor: '#007bff',
        border: '1px solid #0056b3',
        borderRadius: '3px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white',
        fontSize: '10px',
        fontWeight: 'bold',
        textAlign: 'center',
        overflow: 'hidden',
        cursor: isDragging ? 'grabbing' : 'grab',
        zIndex: isDragging ? 1000 : 1,
        ...style
      }}
      {...attributes}
      {...listeners}
      title={`${video.name} (${video.duration.toFixed(1)}s)`}
    >
      <div style={{
        padding: '0 4px',
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis'
      }}>
        {index + 1}
      </div>

      {/* Trim button */}
      {onVideoTrim && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            onVideoTrim(video.id);
          }}
          style={{
            position: 'absolute',
            top: '2px',
            right: '20px',
            width: '16px',
            height: '16px',
            backgroundColor: 'rgba(0, 123, 255, 0.8)',
            color: 'white',
            border: 'none',
            borderRadius: '50%',
            fontSize: '8px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            lineHeight: '1'
          }}
          title="Trim clip"
        >
          ✂
        </button>
      )}

      {/* Delete button */}
      {onVideoDelete && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            onVideoDelete(video.id);
          }}
          style={{
            position: 'absolute',
            top: '2px',
            right: '2px',
            width: '16px',
            height: '16px',
            backgroundColor: 'rgba(255, 0, 0, 0.8)',
            color: 'white',
            border: 'none',
            borderRadius: '50%',
            fontSize: '10px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            lineHeight: '1'
          }}
          title="Delete clip"
        >
          ×
        </button>
      )}
    </div>
  );
};

interface TimelineProps {
  videos: UploadedVideo[];
  totalDurationInFrames: number;
  currentFrame?: number;
  onSeek?: (frame: number) => void;
  onVideoReorder?: (fromIndex: number, toIndex: number) => void;
  onVideoDelete?: (videoId: string) => void;
  onVideoTrim?: (videoId: string, trimStart: number, trimEnd: number) => void;
}

// Simple Trimming Modal Component
interface TrimmingModalProps {
  video: UploadedVideo;
  isOpen: boolean;
  onClose: () => void;
  onTrim: (trimStart: number, trimEnd: number) => void;
}

const TrimmingModal: React.FC<TrimmingModalProps> = ({ video, isOpen, onClose, onTrim }) => {
  const [trimStart, setTrimStart] = useState(video.trimStart || 0);
  const [trimEnd, setTrimEnd] = useState(video.trimEnd || video.durationInFrames);

  if (!isOpen) return null;

  const handleTrim = () => {
    onTrim(trimStart, trimEnd);
    onClose();
  };

  const formatFrameToTime = (frame: number) => {
    const seconds = frame / 30;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = (seconds % 60).toFixed(1);
    return `${minutes}:${remainingSeconds.padStart(4, '0')}`;
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '24px',
        borderRadius: '8px',
        minWidth: '400px',
        maxWidth: '500px'
      }}>
        <h3 style={{ margin: '0 0 16px 0' }}>Trim Video: {video.name}</h3>

        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'block', marginBottom: '8px', fontSize: '14px', fontWeight: 'bold' }}>
            Start Frame: {trimStart} ({formatFrameToTime(trimStart)})
          </label>
          <input
            type="range"
            min={0}
            max={video.durationInFrames - 1}
            value={trimStart}
            onChange={(e) => setTrimStart(Number(e.target.value))}
            style={{ width: '100%' }}
          />
        </div>

        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'block', marginBottom: '8px', fontSize: '14px', fontWeight: 'bold' }}>
            End Frame: {trimEnd} ({formatFrameToTime(trimEnd)})
          </label>
          <input
            type="range"
            min={trimStart + 1}
            max={video.durationInFrames}
            value={trimEnd}
            onChange={(e) => setTrimEnd(Number(e.target.value))}
            style={{ width: '100%' }}
          />
        </div>

        <div style={{ marginBottom: '16px', fontSize: '12px', color: '#666' }}>
          Trimmed Duration: {trimEnd - trimStart} frames ({formatFrameToTime(trimEnd - trimStart)})
        </div>

        <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
          <button
            onClick={onClose}
            style={{
              padding: '8px 16px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              backgroundColor: 'white',
              cursor: 'pointer'
            }}
          >
            Cancel
          </button>
          <button
            onClick={handleTrim}
            style={{
              padding: '8px 16px',
              border: 'none',
              borderRadius: '4px',
              backgroundColor: '#007bff',
              color: 'white',
              cursor: 'pointer'
            }}
          >
            Apply Trim
          </button>
        </div>
      </div>
    </div>
  );
};

export const Timeline: React.FC<TimelineProps> = ({
  videos,
  totalDurationInFrames,
  currentFrame = 0,
  onSeek,
  onVideoReorder,
  onVideoDelete,
  onVideoTrim
}) => {
  const timelineWidth = 800; // Fixed width for now
  const timelineHeight = 80;
  const fps = 30;

  // State for trimming modal
  const [trimmingVideo, setTrimmingVideo] = useState<UploadedVideo | null>(null);

  // Handle trim button click
  const handleTrimClick = (videoId: string) => {
    const video = videos.find(v => v.id === videoId);
    if (video) {
      setTrimmingVideo(video);
    }
  };

  // Handle trim apply
  const handleTrimApply = (trimStart: number, trimEnd: number) => {
    if (trimmingVideo && onVideoTrim) {
      onVideoTrim(trimmingVideo.id, trimStart, trimEnd);
    }
    setTrimmingVideo(null);
  };

  // Set up drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id && onVideoReorder) {
      const oldIndex = videos.findIndex((video) => video.id === active.id);
      const newIndex = videos.findIndex((video) => video.id === over?.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        onVideoReorder(oldIndex, newIndex);
      }
    }
  };

  // Calculate scale factor for timeline
  const pixelsPerFrame = totalDurationInFrames > 0 ? timelineWidth / totalDurationInFrames : 1;

  // Calculate current playback position in pixels
  const playheadPosition = currentFrame * pixelsPerFrame;

  // Handle timeline click for seeking
  const handleTimelineClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!onSeek) return;
    
    const rect = event.currentTarget.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const clickedFrame = Math.round(clickX / pixelsPerFrame);
    const clampedFrame = Math.max(0, Math.min(clickedFrame, totalDurationInFrames));
    
    onSeek(clampedFrame);
  };

  // Calculate video block positions
  let currentStartFrame = 0;
  const videoBlocks = videos.map((video, index) => {
    const blockWidth = video.durationInFrames * pixelsPerFrame;
    const blockLeft = currentStartFrame * pixelsPerFrame;
    
    const block = {
      video,
      index,
      left: blockLeft,
      width: blockWidth,
      startFrame: currentStartFrame,
      endFrame: currentStartFrame + video.durationInFrames
    };
    
    currentStartFrame += video.durationInFrames;
    return block;
  });

  return (
    <div style={{
      width: '100%',
      backgroundColor: '#f8f9fa',
      border: '1px solid #dee2e6',
      borderRadius: '8px',
      padding: '16px',
      marginTop: '20px'
    }}>
      <h3 style={{ 
        margin: '0 0 16px 0', 
        fontSize: '16px',
        color: '#333'
      }}>
        Timeline
      </h3>
      
      {/* Timeline info */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '12px',
        fontSize: '12px',
        color: '#666'
      }}>
        <span>
          {videos.length} clip{videos.length !== 1 ? 's' : ''} • {(totalDurationInFrames / fps).toFixed(1)}s total
        </span>
        <span>
          Current: {(currentFrame / fps).toFixed(1)}s (Frame {currentFrame})
        </span>
      </div>

      {/* Timeline container with drag and drop */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={videos.map(v => v.id)} strategy={horizontalListSortingStrategy}>
          <div style={{
            position: 'relative',
            width: timelineWidth,
            height: timelineHeight,
            backgroundColor: '#e9ecef',
            border: '1px solid #ced4da',
            borderRadius: '4px',
            cursor: 'pointer',
            overflow: 'hidden'
          }}
          onClick={handleTimelineClick}
          >
            {/* Video blocks */}
            {videoBlocks.map((block) => (
              <SortableVideoClip
                key={block.video.id}
                video={block.video}
                index={block.index}
                left={block.left}
                width={block.width}
                timelineHeight={timelineHeight}
                onVideoDelete={onVideoDelete}
                onVideoTrim={handleTrimClick}
              />
            ))}

        {/* Playhead indicator */}
        <div
          style={{
            position: 'absolute',
            left: `${playheadPosition}px`,
            top: '0',
            width: '2px',
            height: '100%',
            backgroundColor: '#dc3545',
            pointerEvents: 'none',
            zIndex: 10
          }}
        />
        
        {/* Playhead handle */}
        <div
          style={{
            position: 'absolute',
            left: `${playheadPosition - 6}px`,
            top: '-4px',
            width: '12px',
            height: '12px',
            backgroundColor: '#dc3545',
            borderRadius: '50%',
            border: '2px solid white',
            pointerEvents: 'none',
            zIndex: 11
          }}
        />
          </div>
        </SortableContext>
      </DndContext>

      {/* Timeline scale */}
      <div style={{
        marginTop: '8px',
        height: '20px',
        position: 'relative',
        width: timelineWidth
      }}>
        {/* Time markers */}
        {Array.from({ length: Math.ceil(totalDurationInFrames / fps) + 1 }, (_, i) => {
          const seconds = i;
          const framePosition = seconds * fps;
          const pixelPosition = framePosition * pixelsPerFrame;

          if (pixelPosition > timelineWidth) return null;

          return (
            <div
              key={i}
              style={{
                position: 'absolute',
                left: `${pixelPosition}px`,
                top: '0',
                fontSize: '10px',
                color: '#666',
                transform: 'translateX(-50%)'
              }}
            >
              {seconds}s
            </div>
          );
        })}
      </div>

      {/* Trimming Modal */}
      {trimmingVideo && (
        <TrimmingModal
          video={trimmingVideo}
          isOpen={true}
          onClose={() => setTrimmingVideo(null)}
          onTrim={handleTrimApply}
        />
      )}
    </div>
  );
};
