import { AbsoluteFill, Sequence, Video, useCurrentFrame } from 'remotion';

// Interface for uploaded video data
interface UploadedVideo {
  id: string;
  file: File;
  name: string;
  src: string; // Object URL for preview
  duration: number; // Duration in seconds
  durationInFrames: number; // Duration in frames (30fps)
}

interface VideoStitchingProps {
  videos?: UploadedVideo[];
}

export const VideoStitching: React.FC<VideoStitchingProps> = ({ videos = [] }) => {
  const frame = useCurrentFrame();
  let currentStartFrame = 0;

  // If no videos are provided, show a placeholder
  if (videos.length === 0) {
    return (
      <AbsoluteFill style={{
        backgroundColor: 'black',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          color: 'white',
          fontSize: 24,
          fontFamily: 'Arial, sans-serif',
          textAlign: 'center'
        }}>
          No videos uploaded yet
          <div style={{ fontSize: 16, marginTop: 10, opacity: 0.7 }}>
            Upload video files to see them here
          </div>
        </div>
      </AbsoluteFill>
    );
  }

  return (
    <AbsoluteFill style={{ backgroundColor: 'black' }}>
      {/* Video sequences */}
      {videos.map((video, index) => {
        const sequence = (
          <Sequence
            key={video.id}
            from={currentStartFrame}
            durationInFrames={video.durationInFrames}
          >
            <AbsoluteFill>
              <Video
                src={video.src}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                }}
                volume={0.5}
              />
              {/* Overlay showing current clip info */}
              <div style={{
                position: 'absolute',
                top: 20,
                left: 20,
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                color: 'white',
                padding: '10px 15px',
                borderRadius: '5px',
                fontSize: 16,
                fontFamily: 'Arial, sans-serif'
              }}>
                {video.name} - Frame: {frame}
                <div style={{ fontSize: 12, opacity: 0.8 }}>
                  Clip {index + 1} of {videos.length} • {video.duration.toFixed(1)}s
                </div>
              </div>
            </AbsoluteFill>
          </Sequence>
        );

        currentStartFrame += video.durationInFrames;
        return sequence;
      })}
    </AbsoluteFill>
  );
};
