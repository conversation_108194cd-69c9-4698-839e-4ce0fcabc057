.timeline-container {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  min-height: 120px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #dee2e6;
}

.timeline-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.timeline-info {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #666;
}

.timeline-track {
  position: relative;
  height: 80px;
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  overflow: hidden;
}

.timeline-ruler {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: #f1f3f4;
  border-bottom: 1px solid #dee2e6;
}

.timeline-marker {
  position: absolute;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.timeline-tick {
  width: 1px;
  height: 8px;
  background: #666;
  margin-top: 2px;
}

.timeline-time {
  font-size: 10px;
  color: #666;
  margin-top: 2px;
  white-space: nowrap;
}

.timeline-clips {
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 4px;
}

.timeline-clip {
  position: absolute;
  height: calc(100% - 8px);
  background: linear-gradient(135deg, #007bff, #0056b3);
  border: 1px solid #0056b3;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 40px;
  overflow: hidden;
}

.timeline-clip:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
  z-index: 10;
}

.timeline-clip-dragging {
  opacity: 0.5;
  transform: rotate(5deg);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.5);
  z-index: 1000;
}

.timeline-clip-content {
  padding: 4px 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  color: white;
  font-size: 11px;
}

.timeline-clip-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 20px);
}

.timeline-clip-duration {
  font-size: 9px;
  opacity: 0.8;
  margin-top: 2px;
}

.timeline-clip-delete {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.timeline-clip:hover .timeline-clip-delete {
  opacity: 1;
}

.timeline-clip-delete:hover {
  background: rgba(255, 0, 0, 0.8);
}

.timeline-playhead {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  pointer-events: none;
  z-index: 20;
}

.timeline-playhead-line {
  width: 100%;
  height: 100%;
  background: #ff4444;
  box-shadow: 0 0 4px rgba(255, 68, 68, 0.5);
}

.timeline-playhead-handle {
  position: absolute;
  top: -4px;
  left: -6px;
  width: 14px;
  height: 14px;
  background: #ff4444;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.timeline-empty {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.timeline-empty p {
  margin: 4px 0;
  font-size: 14px;
}

.timeline-empty p:first-child {
  font-weight: 500;
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .timeline-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .timeline-info {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .timeline-clip-content {
    padding: 2px 4px;
  }
  
  .timeline-clip-name {
    font-size: 10px;
  }
  
  .timeline-clip-duration {
    font-size: 8px;
  }
}
